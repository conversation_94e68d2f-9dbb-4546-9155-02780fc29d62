# MySQL MCP Server - FastMCP版本

高性能、企业级MySQL数据库操作服务器，专为Model Context Protocol (MCP)设计。基于FastMCP框架构建，集成了智能缓存、性能监控、增强型连接池管理和全面的安全防护机制。

## 🚀 核心特性

### ⚡ 高性能架构
- **FastMCP框架**：采用现代化FastMCP构建，提供卓越性能和可靠性
- **智能缓存系统**：多级LRU缓存，支持TTL和访问统计
  - 表结构缓存（128条目）
  - 表存在性检查缓存（64条目）  
  - 索引信息缓存（64条目）
- **增强型连接池**：预创建连接、健康检查和自动重连
- **批处理优化**：可配置批次大小的智能数据获取

### 🧠 高级功能
- **增强重试机制**：指数退避重试策略，可配置重试次数（最多3次）
- **自适应频率限制**：令牌桶算法，支持系统负载自适应调整
- **查询优化**：慢查询检测（可配置阈值，默认1.0秒）和性能分析
- **并发控制**：使用RLock的线程安全资源管理
- **实时性能监控**：时间序列指标收集，支持1000点历史记录
- **增强安全验证**：多级输入验证，包含SQL注入模式检测
- **系统资源监控**：CPU和内存使用率跟踪（需要psutil）

### 🛡️ 企业级安全
- **多层防护体系**：输入验证、SQL注入防护、危险模式检测
- **MySQL错误分类**：智能错误代码分析和分类
- **安全审计**：全面的操作日志和安全事件追踪
- **敏感信息保护**：自动凭据屏蔽和日志清理

### 🔧 零配置架构
- **常量化设计**：所有魔法数字和字符串标准化定义
- **配置分离**：MySQL配置、安全配置、性能配置独立管理
- **环境变量驱动**：完整的环境变量配置支持
- **零魔法数字**：所有常量标准化定义，提高可维护性

## 目录

- [快速开始](#快速开始)
- [安装说明](#安装说明)
- [环境变量配置](#环境变量配置)
- [Claude Desktop集成](#claude-desktop集成)
- [API工具参考](#api工具参考)
- [使用示例](#使用示例)
- [架构设计](#架构设计)
- [性能监控](#性能监控)
- [缓存策略](#缓存策略)
- [安全特性](#安全特性)
- [故障排除](#故障排除)
- [性能调优](#性能调优)
- [开发指南](#开发指南)
- [许可证](#许可证)

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd MySQL_MCP_PY
```

### 2. 安装依赖

使用pip安装：
```bash
pip install -r requirements.txt
```

或使用项目配置安装：
```bash
pip install -e .
```

### 3. 配置环境（使用.env文件 - 推荐）
```bash
# 复制示例配置文件
cp .env.example .env

# 使用您的数据库设置编辑.env文件
# nano .env  # 或使用您喜欢的编辑器
```

### 4. 运行服务器
```bash
python server.py
```

## 安装说明

### 系统要求
- Python 3.11+ (根据 pyproject.toml 要求)
- MySQL 5.7+ (推荐 MySQL 8.0+)
- 最少512MB RAM以获得最佳连接池性能
- FastMCP框架支持

### 可选性能增强
为了提高大数据集的性能，您可以选择安装：
- NumPy 1.21+ (用于增强的百分位数计算)

### 依赖包
核心依赖：
- `fastmcp>=2.0.0`：现代化FastMCP框架
- `mysql-connector-python>=8.0.33`：官方MySQL连接器，支持连接池
- `psutil>=5.9.0`：系统监控增强指标（可选）

开发依赖（可选）：
- `pytest>=7.4.0`：测试框架
- `pytest-asyncio>=0.21.0`：异步测试支持
- `black>=23.0.0`：代码格式化
- `mypy>=1.5.0`：静态类型检查
- `types-psutil>=5.9.0`：psutil类型定义

## 环境变量配置

服务器可以通过两种方式配置：

1. **使用.env文件**（推荐）：将`.env.example`复制为`.env`并自定义值
2. **直接设置环境变量**：在shell中导出变量或在进程配置中设置

请参阅`.env.example`文件，其中包含所有可配置选项的详细说明。

### 🔗 数据库连接配置
| 环境变量 | 描述 | 默认值 |
|---------|------|-------|
| `MYSQL_HOST` | 数据库主机地址 | localhost |
| `MYSQL_PORT` | 数据库端口 | 3306 |
| `MYSQL_USER` | 数据库用户名 | root |
| `MYSQL_PASSWORD` | 数据库密码 | "" |
| `MYSQL_DATABASE` | 数据库名称 | 必须设置 |
| `MYSQL_CONNECTION_LIMIT` | 连接池最大连接数 | 10 |
| `MYSQL_CONNECT_TIMEOUT` | 连接超时时间(秒) | 60 |
| `MYSQL_IDLE_TIMEOUT` | 空闲连接超时时间(秒) | 60 |
| `MYSQL_SSL` | 启用SSL连接 | false |

### 🛡️ 安全配置
| 环境变量 | 描述 | 默认值 |
|---------|------|-------|
| `MAX_QUERY_LENGTH` | 最大查询长度（字符数） | 10000 |
| `ALLOWED_QUERY_TYPES` | 允许的查询类型，逗号分隔 | SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER |
| `MAX_RESULT_ROWS` | 每次查询最大返回行数 | 1000 |
| `QUERY_TIMEOUT` | 查询执行超时时间(秒) | 30 |
| `RATE_LIMIT_WINDOW` | 频率限制时间窗口(秒) | 60 |
| `RATE_LIMIT_MAX` | 时间窗口内最大请求数 | 100 |

### ⚡ 性能配置
| 环境变量 | 描述 | 默认值 |
|---------|------|-------|
| `SCHEMA_CACHE_SIZE` | 表结构缓存大小 | 128 |
| `TABLE_EXISTS_CACHE_SIZE` | 表存在性缓存大小 | 64 |
| `INDEX_CACHE_SIZE` | 索引信息缓存大小 | 64 |
| `CACHE_TTL` | 缓存过期时间(秒) | 300 |
| `BATCH_SIZE` | 批处理大小 | 1000 |
| `SLOW_QUERY_THRESHOLD` | 慢查询阈值(秒) | 1.0 |

## Claude Desktop集成

要将此MySQL MCP服务器添加到您的Claude Desktop配置中：

### 1. 找到配置文件

配置文件位置取决于您的操作系统：

- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

### 2. 使用.env文件配置（推荐）

推荐使用`.env`文件进行配置。将`.env.example`文件复制为`.env`并根据您的环境进行自定义：

```bash
cp .env.example .env
# 然后编辑.env文件配置您的参数
```

然后在Claude Desktop配置中使用简单的配置：

```json
{
  "mcpServers": {
    "mysql-mcp-server": {
      "command": "python",
      "args": ["/path/to/your/MySQL_MCP_PY/server.py"]
    }
  }
}
```

### 3. 基础配置示例（环境变量）

如果您更喜欢直接在配置中设置环境变量：

```json
{
  "mcpServers": {
    "mysql-mcp-server": {
      "command": "python",
      "args": ["/path/to/your/MySQL_MCP_PY/server.py"],
      "env": {
        "MYSQL_HOST": "localhost",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "your_username",
        "MYSQL_PASSWORD": "your_password",
        "MYSQL_DATABASE": "your_database"
      }
    }
  }
}
```

### 4. 高性能生产环境配置

```json
{
  "mcpServers": {
    "mysql-optimized": {
      "command": "python",
      "args": ["/path/to/your/MySQL_MCP_PY/server.py"],
      "env": {
        "MYSQL_HOST": "prod-db-host",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "app_user",
        "MYSQL_PASSWORD": "secure_password",
        "MYSQL_DATABASE": "production_db",
        "MYSQL_CONNECTION_LIMIT": "20",
        "MAX_RESULT_ROWS": "2000",
        "RATE_LIMIT_MAX": "200",
        "SCHEMA_CACHE_SIZE": "256",
        "MYSQL_SSL": "true",
        "SLOW_QUERY_THRESHOLD": "0.5"
      }
    }
  }
}
```

### 5. 安全受限配置（只读权限）

```json
{
  "mcpServers": {
    "mysql-readonly": {
      "command": "python",
      "args": ["/path/to/your/MySQL_MCP_PY/server.py"],
      "env": {
        "MYSQL_HOST": "your-db-host",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "readonly_user",
        "MYSQL_PASSWORD": "secure_password",
        "MYSQL_DATABASE": "your_database",
        "MAX_RESULT_ROWS": "500",
        "RATE_LIMIT_MAX": "50",
        "ALLOWED_QUERY_TYPES": "SELECT,SHOW,DESCRIBE",
        "MYSQL_SSL": "true"
      }
    }
  }
}
```

## API工具参考

### 🔧 核心数据操作工具

| 工具名称 | 描述 | 参数 | 缓存支持 | 性能优化 |
|---------|------|------|----------|----------|
| `mysql_query` | 执行自定义SQL查询，支持预处理参数 | `query`: SQL字符串, `params`: 可选参数 | 否 | 批处理、重试机制 |
| `mysql_show_tables` | 显示数据库所有表 | 无 | 否 | 连接池优化 |
| `mysql_describe_table` | 获取表结构详情 | `table_name`: 表名 | 是 | 智能缓存、TTL管理 |
| `mysql_select_data` | 高级数据选择，支持条件和限制 | `table_name`, `columns`, `where_clause`, `limit` | 否 | 批处理、结果限制 |
| `mysql_insert_data` | 向表插入新数据 | `table_name`, `data`: 字典 | 否 | 参数化查询、输入验证 |
| `mysql_update_data` | 更新表中现有数据 | `table_name`, `data`, `where_clause` | 否 | 条件验证、安全检查 |
| `mysql_delete_data` | 从表中删除数据 | `table_name`, `where_clause` | 否 | 条件验证、安全检查 |

### 📊 模式管理工具

| 工具名称 | 描述 | 参数 | 缓存支持 | 特殊功能 |
|---------|------|------|----------|----------|
| `mysql_get_schema` | 获取数据库模式信息（表、列、约束） | `table_name`: 可选 | 否 | 支持单表或全库查询 |
| `mysql_get_indexes` | 获取表索引信息 | `table_name`: 可选 | 是 | 智能缓存、性能统计 |
| `mysql_get_foreign_keys` | 获取外键约束信息 | `table_name`: 可选 | 否 | 完整约束关系 |
| `mysql_create_table` | 创建新表，支持完整列定义 | `table_name`, `columns`: 列表 | 否 | 自动缓存清理 |
| `mysql_drop_table` | 删除表，支持 IF EXISTS | `table_name`, `if_exists`: 布尔值 | 否 | 自动缓存清理 |

### 🔍 监控诊断工具

| 工具名称 | 描述 | 参数 | 返回信息 |
|---------|------|------|----------|
| `mysql_diagnose_connection` | 全面系统诊断，包含增强指标 | 无 | 连接池状态、配置信息、性能指标、连接测试、增强时间序列指标 |

## 使用示例

### 基本数据库操作

```python
# 显示所有表
mysql_show_tables()

# 描述表结构（带缓存提升性能）
mysql_describe_table(table_name="users")

# 带条件查询数据
mysql_select_data(
    table_name="users", 
    columns=["name", "email"], 
    where_clause="age > 18", 
    limit=10
)

# 执行自定义查询
mysql_query(
    query="SELECT COUNT(*) FROM users WHERE status = %s",
    params=["active"]
)
```

### 数据修改操作

```python
# 插入新记录
mysql_insert_data(
    table_name="users",
    data={
        "name": "张三",
        "email": "<EMAIL>",
        "age": 25,
        "status": "active"
    }
)

# 更新现有记录
mysql_update_data(
    table_name="users",
    data={"age": 26, "status": "verified"},
    where_clause="email = '<EMAIL>'"
)

# 删除记录
mysql_delete_data(
    table_name="users",
    where_clause="age < 18"
)
```

### 高级模式管理

```python
# 获取完整数据库模式信息
mysql_get_schema()  # 所有表
mysql_get_schema(table_name="users")  # 特定表

# 获取索引信息（智能缓存）
mysql_get_indexes(table_name="users")  # 特定表索引
mysql_get_indexes()  # 所有表索引

# 创建新表（完整列定义）
mysql_create_table(
    table_name="products",
    columns=[
        {
            "name": "id", 
            "type": "INT", 
            "primary_key": True, 
            "auto_increment": True,
            "nullable": False
        },
        {
            "name": "name", 
            "type": "VARCHAR(255)", 
            "nullable": False
        },
        {
            "name": "price", 
            "type": "DECIMAL(10,2)", 
            "nullable": False
        },
        {
            "name": "created_at", 
            "type": "TIMESTAMP", 
            "nullable": False,
            "default": "CURRENT_TIMESTAMP"
        }
    ]
)

# 安全删除表
mysql_drop_table(
    table_name="temp_table",
    if_exists=True  # 避免表不存在错误
)
```

### 性能监控和诊断

```python
# 全面系统诊断，包含增强指标
diagnosis = mysql_diagnose_connection()
print(f"缓存命中率: {diagnosis['performance_metrics']['performance']['cache_hit_rate']:.2%}")
print(f"平均查询时间: {diagnosis['performance_metrics']['performance']['avg_query_time']:.3f}s")
print(f"连接状态: {diagnosis['connection_test']['status']}")
print(f"可用连接: {diagnosis['connection_pool_status']['available_connections']}")
print(f"连接池大小: {diagnosis['connection_pool_status']['pool_size']}")
print(f"健康检查: {diagnosis['connection_pool_status']['health_check_active']}")

# 访问增强时间序列指标
enhanced_metrics = diagnosis.get('enhanced_metrics', {})
if enhanced_metrics:
    print(f"查询性能统计: {enhanced_metrics.get('query_performance', {})}")
    print(f"错误统计: {enhanced_metrics.get('error_statistics', {})}")
    print(f"缓存性能: {enhanced_metrics.get('cache_performance', {})}")
    print(f"系统指标: {enhanced_metrics.get('system_metrics', {})}")
```

## 架构设计

### 🏗️ 核心组件架构

```
MySQLManager
├── ConfigurationManager         # 集中配置管理
├── ConnectionPool               # 增强型连接池管理
├── SmartCache                   # 智能缓存系统
│   ├── SchemaCache              # 表结构缓存
│   ├── TableExistsCache         # 表存在性缓存
│   └── IndexCache               # 索引信息缓存
├── PerformanceMetrics           # 传统性能指标收集器
├── MetricsManager               # 时间序列指标与告警
│   ├── TimeSeriesMetrics        # 个人指标收集器
│   └── AlertingSystem           # 可配置告警规则
├── SecurityValidator            # 多级输入验证
├── AdaptiveRateLimiter          # 令牌桶频率限制
└── RetryStrategy                # 指数退避重试逻辑
```

### 🔄 数据流优化
1. **请求接收** → 频率限制检查 → 输入验证
2. **缓存查询** → 缓存命中/未命中统计
3. **连接获取** → 连接池健康检查 → 查询执行
4. **结果处理** → 批处理优化 → 性能指标更新
5. **资源清理** → 连接释放 → 缓存管理

### 📊 常量化设计

服务器采用零魔法数字设计，所有配置常量集中定义：

```python
class DefaultConfig:
    MYSQL_PORT = 3306
    CONNECTION_LIMIT = 10
    MAX_QUERY_LENGTH = 10000
    MAX_RESULT_ROWS = 1000
    SCHEMA_CACHE_SIZE = 128
    CACHE_TTL = 300
    # ... 更多常量

class StringConstants:
    DEFAULT_HOST = "localhost"
    DEFAULT_USER = "root"
    CHARSET = "utf8mb4"
    SQL_MODE = "TRADITIONAL"
    # ... 更多字符串常量

class MySQLErrorCodes:
    ACCESS_DENIED = 1045
    DUPLICATE_ENTRY = 1062
    PARSE_ERROR = 1064
    # ... 更多错误代码
```

### 🔧 配置管理系统

`ConfigurationManager` 提供集中的、经过验证的配置：

```python
# 自动验证和类型检查
config_manager = ConfigurationManager()
database_config = config_manager.database  # DatabaseConfig 实例
security_config = config_manager.security  # SecurityConfig 实例
cache_config = config_manager.cache        # CacheConfig 实例

# 配置诊断
config_summary = config_manager.get_summary()
config_dict = config_manager.to_dict()  # 敏感数据已屏蔽
```

### 📝 日志配置

服务器支持通过环境变量配置日志：

| 环境变量 | 描述 | 默认值 |
|---------|------|-------|
| `LOG_LEVEL` | 日志级别 (DEBUG, INFO, WARNING, ERROR) | INFO |
| `LOGGER_NAME` | 日志记录器名称 | mysql_mcp |

配置示例：
```bash
export LOG_LEVEL=DEBUG
export LOGGER_NAME=my_mysql_mcp
```

## 性能监控

### 🎯 关键性能指标

#### 查询性能分析
- **平均查询时间**：所有查询的平均执行时间
- **慢查询识别**：超过阈值的查询自动标记
- **错误率监控**：查询失败率统计和趋势分析
- **并发性能**：多线程查询处理效率

#### 缓存效率监控
```python
# 监控缓存性能
diagnosis = mysql_diagnose_connection()
cache_hit_rate = diagnosis['performance_metrics']['cache_stats']['schema_cache']['hit_rate']

# 缓存命中率 > 80% 为良好性能
# 缓存命中率 < 50% 需要调优
```

#### 连接池健康状态
- **连接利用率**：活跃连接占比
- **等待时间分析**：连接获取延迟统计
- **连接健康检查**：自动故障连接检测
- **池扩展建议**：基于使用模式的配置建议

### 📈 性能指标示例

```json
{
  "performance": {
    "query_count": 1250,
    "avg_query_time": 0.045,
    "slow_query_count": 12,
    "error_rate": 0.008,
    "cache_hit_rate": 0.847
  },
  "cache_stats": {
    "schema_cache": {
      "size": 45,
      "max_size": 128,
      "hit_rate": 0.892,
      "ttl": 300
    }
  },
  "connection_pool": {
    "pool_size": 10,
    "available_connections": 7,
    "connection_stats": {
      "pool_hits": 890,
      "pool_waits": 23
    }
  }
}
```

## 缓存策略

### 🧠 多级智能缓存

#### 表结构缓存（SchemaCache）
- **缓存内容**：列定义、数据类型、约束、注释
- **TTL**：5分钟（可配置）
- **失效策略**：DDL操作后自动清理
- **统计指标**：命中率、访问频次

#### 表存在性缓存（ExistsCache）
- **缓存内容**：布尔值表示表是否存在
- **使用场景**：频繁的表验证操作
- **失效时机**：CREATE/DROP TABLE操作

#### 索引信息缓存（IndexCache）
- **缓存内容**：索引名称、列、类型、唯一性
- **性能影响**：显著减少INFORMATION_SCHEMA查询
- **智能更新**：ALTER TABLE操作后自动刷新

### 缓存管理最佳实践

```python
# 缓存调优建议
SCHEMA_CACHE_SIZE = 128      # 适合中等规模应用
TABLE_EXISTS_CACHE_SIZE = 64 # 快速存在性检查
INDEX_CACHE_SIZE = 64        # 索引查询优化
CACHE_TTL = 300              # 5分钟过期平衡性能和一致性
```

## 安全特性

### 🔒 多层安全防护体系

#### 输入验证和清理
- **空字节过滤**：防止字符串截断攻击
- **长度限制**：防止缓冲区溢出
- **危险模式检测**：识别潜在的SQL注入尝试
- **字符编码验证**：确保UTF-8兼容性

#### MySQL错误智能分类
```python
class MySQLErrorCodes:
    ACCESS_DENIED = 1045           # 访问权限错误
    UNKNOWN_DATABASE = 1049        # 数据库不存在
    DUPLICATE_ENTRY = 1062         # 重复条目错误
    PARSE_ERROR = 1064             # SQL语法错误
    CANT_CONNECT_TO_SERVER = 2003  # 连接错误
```

#### 频率限制算法
- **滑动窗口**：60秒时间窗口
- **频率控制**：默认100请求/分钟
- **防护功能**：防止暴力攻击和资源滥用
- **可配置阈值**：根据需求调整限制

### 🛡️ 安全监控和审计
- **操作日志**：所有SQL操作完整记录
- **安全事件**：异常操作自动标记
- **凭据保护**：日志中敏感信息自动屏蔽
- **连接审计**：连接建立和断开追踪

## 故障排除

### 🔧 常见问题解决

#### 1. 数据库连接失败
```
错误: MySQL连接错误: Can't connect to MySQL server
```
**解决方案**：
- 验证MySQL服务正在运行：`systemctl status mysql`
- 检查环境变量中的连接参数
- 测试连接：`telnet <host> <port>`
- 使用 `mysql_diagnose_connection()` 进行详细分析

#### 2. 缓存命中率低
```bash
# 症状：频繁的INFORMATION_SCHEMA查询
# 解决方案：
export SCHEMA_CACHE_SIZE=256
export CACHE_TTL=600
# 重启服务器应用新配置
```

#### 3. 连接池耗尽
```bash
# 症状：Connection pool exhausted
# 诊断：使用mysql_diagnose_connection检查
# 解决方案：
export MYSQL_CONNECTION_LIMIT=20
export MYSQL_CONNECT_TIMEOUT=120
```

#### 4. 频率限制触发
```
错误: Rate limit exceeded. Please try again later.
```
**解决方案**：
- 降低请求频率
- 增加 `RATE_LIMIT_MAX` 设置
- 延长 `RATE_LIMIT_WINDOW` 持续时间

### 🔍 高级诊断工具

```python
# 使用全面诊断进行系统分析
diagnosis = mysql_diagnose_connection()

# 检查关键指标
performance = diagnosis['performance_metrics']['performance']
print(f"查询错误率: {performance['error_rate']:.3f}")
print(f"缓存命中率: {performance['cache_hit_rate']:.3f}")
print(f"平均查询时间: {performance['avg_query_time']:.3f}s")

# 连接池健康检查
pool_stats = diagnosis['connection_pool_status']
print(f"可用连接: {pool_stats['available_connections']}")
if 'connection_stats' in pool_stats:
    print(f"连接池等待: {pool_stats['connection_stats']['pool_waits']}")
```

## 性能调优

### ⚡ 配置模板

#### 大型企业环境
```bash
# 高并发、大数据量配置
MYSQL_CONNECTION_LIMIT=50
MAX_RESULT_ROWS=5000
RATE_LIMIT_MAX=500
SCHEMA_CACHE_SIZE=512
TABLE_EXISTS_CACHE_SIZE=256
INDEX_CACHE_SIZE=256
BATCH_SIZE=2000
SLOW_QUERY_THRESHOLD=0.3
CACHE_TTL=600
```

#### 中等规模应用
```bash
# 平衡性能和资源消耗
MYSQL_CONNECTION_LIMIT=20
MAX_RESULT_ROWS=2000
RATE_LIMIT_MAX=200
SCHEMA_CACHE_SIZE=128
TABLE_EXISTS_CACHE_SIZE=64
INDEX_CACHE_SIZE=64
BATCH_SIZE=1000
SLOW_QUERY_THRESHOLD=1.0
CACHE_TTL=300
```

#### 资源受限环境
```bash
# 低内存、低CPU配置
MYSQL_CONNECTION_LIMIT=5
MAX_RESULT_ROWS=500
RATE_LIMIT_MAX=50
SCHEMA_CACHE_SIZE=32
TABLE_EXISTS_CACHE_SIZE=16
INDEX_CACHE_SIZE=16
BATCH_SIZE=100
SLOW_QUERY_THRESHOLD=2.0
CACHE_TTL=180
```

### 📈 性能监控和调优

#### 关键性能指标

| 指标 | 目标值 | 警告阈值 |
|------|--------|----------|
| 缓存命中率 | > 80% | < 60% |
| 平均查询时间 | < 100ms | > 500ms |
| 错误率 | < 1% | > 5% |
| 连接池等待率 | < 10% | > 30% |
| 慢查询率 | < 5% | > 15% |

#### 性能监控

要监控MySQL MCP服务器的性能，可以使用`mysql_diagnose_connection()`工具，它提供全面的指标包括：

1. **查询性能**：平均查询时间、慢查询数量、错误率
2. **缓存效率**：表结构、表存在性、索引缓存的命中率
3. **连接池健康状态**：可用连接数、连接池命中与等待情况
4. **系统资源**：CPU和内存使用情况（如果安装了psutil）

使用示例：
```python
diagnosis = mysql_diagnose_connection()
print(f"缓存命中率: {diagnosis['performance_metrics']['performance']['cache_hit_rate']:.2%}")
print(f"平均查询时间: {diagnosis['performance_metrics']['performance']['avg_query_time']:.3f}s")
```

#### 性能调优指南

1. **缓存调优**：
   - 如果缓存命中率 < 80%，考虑增加缓存大小（`SCHEMA_CACHE_SIZE`、`TABLE_EXISTS_CACHE_SIZE`、`INDEX_CACHE_SIZE`）
   - 如果内存使用率高，考虑减少缓存大小或TTL（`CACHE_TTL`）

2. **连接池调优**：
   - 如果连接池等待率 > 10%，增加`MYSQL_CONNECTION_LIMIT`
   - 对于低流量应用，减少`MYSQL_MIN_CONNECTIONS`以节省资源

3. **查询性能调优**：
   - 如果平均查询时间 > 100ms，检查MySQL慢查询日志并优化查询
   - 调整`SLOW_QUERY_THRESHOLD`以匹配性能要求
   - 增加`BATCH_SIZE`以提高批量操作效率，但需监控内存使用

4. **频率限制调优**：
   - 对于高吞吐量应用，增加`RATE_LIMIT_MAX`
   - 对于更严格的安全要求，减少`RATE_LIMIT_MAX`和/或`RATE_LIMIT_WINDOW`

## 开发指南

### 🏗️ 架构原则
1. **性能优先**：所有设计决策优先考虑性能影响
2. **安全第一**：多层安全验证，永不信任用户输入
3. **可观测性**：全面的监控和诊断能力
4. **配置驱动**：通过环境变量实现灵活配置
5. **优雅降级**：缓存失败不影响核心功能

### 🔧 扩展开发

```python
# 添加新的缓存类型
class CustomCache(SmartCache):
    def __init__(self, max_size: int):
        super().__init__(max_size)
        # 自定义缓存逻辑

# 扩展性能指标
@dataclass
class ExtendedMetrics(PerformanceMetrics):
    custom_metric: int = 0
    
    def update_custom_metric(self, value: int):
        self.custom_metric += value
```

### 贡献指南

我们欢迎为改进MySQL MCP FastMCP服务器做出贡献！

#### 开发环境设置
```bash
# 克隆项目
git clone <repository-url>
cd MySQL_MCP_FastMCP

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装开发依赖
pip install -e ".[dev]"

# 运行测试
pytest tests/

# 代码格式化
black server.py

# 类型检查
mypy server.py
```

#### 贡献指南
- 遵循PEP 8代码风格指南
- 为新功能添加测试
- 为更改更新文档
- 确保所有常量都正确定义
- 在新功能中包含安全考虑

## 许可证

MIT License

---

## 架构决策记录

- **FastMCP框架**：选择用于现代异步支持和性能
- **LRU缓存**：实现智能元数据缓存
- **基于常量的配置**：消除魔法数字和字符串
- **全面错误处理**：提供可操作的错误消息
- **安全优先设计**：多层保护

---

**使用FastMCP框架精心构建 ⚡ 为企业级应用提供卓越性能和可靠性**