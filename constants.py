#!/usr/bin/env python3

"""
MySQL MCP服务器常量定义
包含MySQL错误代码、默认配置、字符串常量等
"""


# MySQL错误代码常量
class MySQLErrorCodes:
    # 访问权限错误
    ACCESS_DENIED = 1045
    ACCESS_DENIED_FOR_USER = 1044
    TABLE_ACCESS_DENIED = 1142
    COLUMN_ACCESS_DENIED = 1143
    
    # 对象不存在错误
    UNKNOWN_DATABASE = 1049
    TABLE_DOESNT_EXIST = 1146
    UNKNOWN_COLUMN = 1054
    UNKNOWN_TABLE = 1109
    
    # 约束违反错误
    DUPLICATE_ENTRY = 1062
    DUPLICATE_ENTRY_WITH_KEY_NAME = 1586
    DUPLICATE_KEY_NAME = 1557
    CONSTRAINT_CHECK_FAILED = 1101
    
    # 语法错误
    PARSE_ERROR = 1064
    SYNTAX_ERROR = 1149
    PARSE_ERROR_NEAR = 1065
    
    # 连接错误
    CANT_CONNECT_TO_SERVER = 2003
    LOST_CONNECTION = 2013
    SERVER_HAS_GONE_AWAY = 2006
    CONNECTION_TIMED_OUT = 2002
    
    # 数据类型错误
    DATA_TRUNCATED = 1265
    INVALID_DEFAULT_VALUE = 1067
    WRONG_VALUE_COUNT = 1136
    
    # 锁相关错误
    LOCK_WAIT_TIMEOUT = 1205
    DEADLOCK = 1213
    
    # 资源限制错误
    TOO_MANY_CONNECTIONS = 1040
    OUT_OF_MEMORY = 1037
    DISK_FULL = 1021


# 默认配置常量
class DefaultConfig:
    # MySQL连接配置
    MYSQL_PORT = 3306
    CONNECTION_LIMIT = 10
    MIN_CONNECTIONS = 2
    CONNECT_TIMEOUT = 60
    IDLE_TIMEOUT = 60
    
    # 安全配置
    MAX_QUERY_LENGTH = 10000
    MAX_RESULT_ROWS = 1000
    QUERY_TIMEOUT = 30
    RATE_LIMIT_WINDOW = 60
    RATE_LIMIT_MAX = 100
    
    # 验证配置
    MAX_INPUT_LENGTH = 1000
    MAX_TABLE_NAME_LENGTH = 64
    
    # 缓存配置
    SCHEMA_CACHE_SIZE = 128
    TABLE_EXISTS_CACHE_SIZE = 64
    INDEX_CACHE_SIZE = 64
    CACHE_TTL = 300  # 缓存过期时间(秒)
    
    # 连接池配置
    HEALTH_CHECK_INTERVAL = 30
    CONNECTION_MAX_AGE = 3600
    
    # 性能监控配置
    METRICS_WINDOW_SIZE = 1000
    SLOW_QUERY_THRESHOLD = 1.0
    
    # 重试配置
    RECONNECT_ATTEMPTS = 3
    RECONNECT_DELAY = 1
    MAX_RETRY_ATTEMPTS = 3
    MAX_RETRY_DELAY = 10.0
    
    # 批处理配置
    BATCH_SIZE = 1000
    
    # 日志配置
    MAX_LOG_DETAIL_LENGTH = 100
    DEFAULT_LOG_LEVEL = "INFO"
    DEFAULT_LOGGER_NAME = "mysql_mcp"
    
    # 性能优化配置
    PERCENTILE_SMALL_ARRAY_THRESHOLD = 100
    HEALTH_CHECK_INTERVAL = 30.0
    CONNECTION_ACQUISITION_WARNING_THRESHOLD = 0.1
    SLOW_QUERY_THRESHOLD = 1.0


# 字符串常量类
class StringConstants:
    # 数据库配置相关字符串
    DEFAULT_HOST = "localhost"
    DEFAULT_USER = "root"
    DEFAULT_PASSWORD = ""
    DEFAULT_DATABASE = ""
    POOL_NAME = "mysql_mcp_pool"
    CHARSET = "utf8mb4"
    SQL_MODE = "TRADITIONAL"
    
    # 环境变量键名
    ENV_MYSQL_HOST = "MYSQL_HOST"
    ENV_MYSQL_PORT = "MYSQL_PORT"
    ENV_MYSQL_USER = "MYSQL_USER"
    ENV_MYSQL_PASSWORD = "MYSQL_PASSWORD"
    ENV_MYSQL_DATABASE = "MYSQL_DATABASE"
    ENV_MYSQL_SSL = "MYSQL_SSL"
    ENV_MYSQL_SSL_CA = "MYSQL_SSL_CA"
    ENV_MYSQL_SSL_CERT = "MYSQL_SSL_CERT"
    ENV_MYSQL_SSL_KEY = "MYSQL_SSL_KEY"
    ENV_CONNECTION_LIMIT = "MYSQL_CONNECTION_LIMIT"
    ENV_MIN_CONNECTIONS = "MYSQL_MIN_CONNECTIONS"
    ENV_CONNECT_TIMEOUT = "MYSQL_CONNECT_TIMEOUT"
    ENV_IDLE_TIMEOUT = "MYSQL_IDLE_TIMEOUT"
    ENV_CONNECTION_MAX_AGE = "MYSQL_CONNECTION_MAX_AGE"
    ENV_ALLOWED_QUERY_TYPES = "ALLOWED_QUERY_TYPES"
    ENV_MAX_QUERY_LENGTH = "MAX_QUERY_LENGTH"
    ENV_MAX_RESULT_ROWS = "MAX_RESULT_ROWS"
    ENV_QUERY_TIMEOUT = "QUERY_TIMEOUT"
    ENV_RATE_LIMIT_WINDOW = "RATE_LIMIT_WINDOW"
    ENV_RATE_LIMIT_MAX = "RATE_LIMIT_MAX"
    ENV_MAX_RETRY_DELAY = "MAX_RETRY_DELAY"
    ENV_LOG_LEVEL = "LOG_LEVEL"
    ENV_LOGGER_NAME = "LOGGER_NAME"
    
    # 性能优化环境变量
    ENV_PERCENTILE_SMALL_ARRAY_THRESHOLD = "PERCENTILE_SMALL_ARRAY_THRESHOLD"
    ENV_HEALTH_CHECK_INTERVAL = "HEALTH_CHECK_INTERVAL"
    ENV_CONNECTION_ACQUISITION_WARNING_THRESHOLD = "CONNECTION_ACQUISITION_WARNING_THRESHOLD"
    ENV_SLOW_QUERY_THRESHOLD = "SLOW_QUERY_THRESHOLD"
    
    # SQL查询类型
    SQL_SELECT = "SELECT"
    SQL_SHOW = "SHOW"
    SQL_DESCRIBE = "DESCRIBE"
    SQL_INSERT = "INSERT"
    SQL_UPDATE = "UPDATE"
    SQL_DELETE = "DELETE"
    SQL_CREATE = "CREATE"
    SQL_DROP = "DROP"
    SQL_ALTER = "ALTER"
    
    # 默认允许的查询类型
    DEFAULT_ALLOWED_QUERY_TYPES = "SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER"
    
    # 危险SQL模式字符串
    DANGEROUS_PATTERNS = ["--", "/*", "*/", "xp_", "sp_"]
    
    # 错误类别
    ERROR_CATEGORY_ACCESS_DENIED = "access_denied"
    ERROR_CATEGORY_OBJECT_NOT_FOUND = "object_not_found"
    ERROR_CATEGORY_CONSTRAINT_VIOLATION = "constraint_violation"
    ERROR_CATEGORY_SYNTAX_ERROR = "syntax_error"
    ERROR_CATEGORY_CONNECTION_ERROR = "connection_error"
    ERROR_CATEGORY_UNKNOWN = "unknown"
    
    # 错误严重级别
    SEVERITY_HIGH = "high"
    SEVERITY_MEDIUM = "medium"
    SEVERITY_LOW = "low"
    
    # 日志事件类型
    LOG_EVENT_SECURITY = "[SECURITY]"
    LOG_EVENT_DATABASE = "[DATABASE]"
    LOG_EVENT_CACHE = "[CACHE]"
    LOG_EVENT_CONNECTION = "[CONNECTION]"
    LOG_EVENT_PERFORMANCE = "[PERFORMANCE]"
    LOG_EVENT_ERROR = "[ERROR]"
    
    # 错误消息模板
    MSG_DATABASE_ACCESS_DENIED = "数据库访问被拒绝，请检查用户名和密码"
    MSG_DATABASE_OBJECT_NOT_FOUND = "数据库对象不存在"
    MSG_DATABASE_CONSTRAINT_VIOLATION = "数据约束违反"
    MSG_DATABASE_SYNTAX_ERROR = "SQL语法错误"
    MSG_DATABASE_CONNECTION_ERROR = "数据库连接错误"
    MSG_MYSQL_CONNECTION_POOL_FAILED = "MySQL连接池创建失败"
    MSG_MYSQL_CONNECTION_ERROR = "MySQL连接错误"
    MSG_MYSQL_QUERY_ERROR = "MySQL查询错误"
    MSG_RATE_LIMIT_EXCEEDED = "超过速率限制，请稍后再试。"
    MSG_QUERY_TOO_LONG = "查询超出最大允许长度"
    MSG_PROHIBITED_OPERATIONS = "查询包含禁止的操作"
    MSG_QUERY_TYPE_NOT_ALLOWED = "查询类型'{query_type}'不允许"
    MSG_INVALID_TABLE_NAME = "无效的表名格式"
    MSG_TABLE_NAME_TOO_LONG = "表名超出最大长度"
    MSG_INVALID_CHARACTER = "{field_name}中存在无效字符"
    MSG_INPUT_TOO_LONG = "{field_name}超出最大长度"
    
    # 状态字符串
    STATUS_SUCCESS = "success"
    STATUS_FAILED = "failed"
    STATUS_NOT_INITIALIZED = "not_initialized"
    STATUS_ERROR = "error"
    STATUS_KEY = "status"
    ERROR_KEY = "error"
    
    # 特殊值
    NULL_BYTE = '\x00'
    TRUE_STRING = "true"
    
    # 服务器相关常量
    SERVER_NAME = "mysql-mcp-server"
    
    # 错误消息常量
    MSG_FASTMCP_NOT_INSTALLED = "错误: FastMCP未安装。请使用以下命令安装: pip install fastmcp"
    MSG_ERROR_DURING_CLEANUP = "Error during cleanup:"
    MSG_SIGNAL_RECEIVED = "收到信号"
    MSG_GRACEFUL_SHUTDOWN = "正在优雅关闭..."
    MSG_SERVER_RUNNING = "MySQL MCP服务器 (FastMCP) 在stdio上运行"
    MSG_SERVER_ERROR = "服务器错误:"
    
    # 工具函数错误消息
    MSG_QUERY_FAILED = "查询失败:"
    MSG_SHOW_TABLES_FAILED = "显示表失败:"
    MSG_DESCRIBE_TABLE_FAILED = "描述表失败:"
    MSG_GET_METRICS_FAILED = "获取性能指标失败:"
    MSG_SELECT_DATA_FAILED = "选择数据失败:"
    MSG_INSERT_DATA_FAILED = "插入数据失败:"
    MSG_UPDATE_DATA_FAILED = "更新数据失败:"
    MSG_DELETE_DATA_FAILED = "删除数据失败:"
    MSG_GET_SCHEMA_FAILED = "获取模式失败:"
    MSG_GET_INDEXES_FAILED = "获取索引失败:"
    MSG_GET_FOREIGN_KEYS_FAILED = "获取外键失败:"
    MSG_CREATE_TABLE_FAILED = "创建表失败:"
    MSG_DROP_TABLE_FAILED = "删除表失败:"
    MSG_DIAGNOSE_FAILED = "诊断失败:"
    MSG_GET_POOL_STATUS_FAILED = "获取连接池状态失败:"
    
    # 连接池相关常量
    MSG_FAILED_TO_INIT_POOL = "Failed to initialize connection pool:"
    
    # JSON响应字段常量
    SUCCESS_KEY = "success"
    
    # SQL关键字常量
    SQL_IF_EXISTS = "IF EXISTS"
    
    # 性能指标字段常量
    FIELD_QUERY_COUNT = "query_count"
    FIELD_AVG_QUERY_TIME = "avg_query_time"
    FIELD_SLOW_QUERY_COUNT = "slow_query_count"
    FIELD_ERROR_COUNT = "error_count"
    FIELD_ERROR_RATE = "error_rate"
    FIELD_CACHE_HIT_RATE = "cache_hit_rate"
    FIELD_CONNECTION_POOL_HITS = "connection_pool_hits"
    FIELD_CONNECTION_POOL_WAITS = "connection_pool_waits"
    
    # 缓存统计字段常量
    FIELD_SIZE = "size"
    FIELD_MAX_SIZE = "max_size"
    FIELD_HIT_COUNT = "hit_count"
    FIELD_MISS_COUNT = "miss_count"
    FIELD_HIT_RATE = "hit_rate"
    FIELD_TTL = "ttl"
    
    # 连接池统计字段常量
    FIELD_POOL_NAME = "pool_name"
    FIELD_POOL_SIZE = "pool_size"
    FIELD_AVAILABLE_CONNECTIONS = "available_connections"
    FIELD_CONNECTION_STATS = "connection_stats"
    FIELD_HEALTH_CHECK_ACTIVE = "health_check_active"
    FIELD_POOL_HITS = "pool_hits"
    FIELD_POOL_WAITS = "pool_waits"
    
    # 性能报告分类常量
    SECTION_PERFORMANCE = "performance"
    SECTION_CACHE_STATS = "cache_stats"
    SECTION_CONNECTION_POOL = "connection_pool"
    SECTION_SCHEMA_CACHE = "schema_cache"
    SECTION_TABLE_EXISTS_CACHE = "table_exists_cache"
    SECTION_INDEX_CACHE = "index_cache"
    
    # 配置字段常量
    FIELD_HOST = "host"
    FIELD_PORT = "port"
    FIELD_DATABASE = "database"
    FIELD_CONNECTION_LIMIT = "connection_limit"
    FIELD_CONNECT_TIMEOUT = "connect_timeout"
    FIELD_SSL_ENABLED = "ssl_enabled"
    
    # 诊断报告字段常量
    FIELD_CONNECTION_POOL_STATUS = "connection_pool_status"
    FIELD_CONFIG = "config"
    FIELD_SECURITY_CONFIG = "security_config"
    FIELD_PERFORMANCE_METRICS = "performance_metrics"
    FIELD_CONNECTION_TEST = "connection_test"
    FIELD_MAX_QUERY_LENGTH = "max_query_length"
    FIELD_MAX_RESULT_ROWS = "max_result_rows"
    FIELD_RATE_LIMIT_MAX = "rate_limit_max"
    FIELD_ALLOWED_QUERY_TYPES = "allowed_query_types"
    FIELD_RESULT = "result"