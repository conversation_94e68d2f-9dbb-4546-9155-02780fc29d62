# MySQL MCP服务器 - 环境变量配置示例
# =============================================================================

# 使用说明:
# 1. 复制此文件为 .env: cp .env.example .env
# 2. 根据你的环境更新配置值
# 3. 服务器启动时会自动加载 .env 文件中的配置
# 4. 也可以通过直接设置环境变量来覆盖这些值

# 配置优先级（从高到低）:
# 1. 直接设置的环境变量
# 2. .env 文件中的配置
# 3. 代码中的默认值

# 注意事项:
# - 密码等敏感信息不应提交到版本控制系统
# - 生产环境中建议使用更严格的权限设置
# - 修改配置后需要重启服务器才能生效

# =============================================================================
# 数据库连接配置
# =============================================================================
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password_here
MYSQL_DATABASE=your_database_name

# 连接池设置
MYSQL_CONNECTION_LIMIT=10          # 连接池最大连接数
MYSQL_MIN_CONNECTIONS=2            # 连接池最小连接数
MYSQL_CONNECT_TIMEOUT=60           # 连接超时时间（秒）
MYSQL_IDLE_TIMEOUT=60              # 空闲连接超时时间（秒）
MYSQL_CONNECTION_MAX_AGE=3600      # 连接最大存活时间（秒）

# SSL配置（取消注释以启用）
# MYSQL_SSL=true
# MYSQL_SSL_CA=/path/to/ca.pem      # SSL CA证书路径
# MYSQL_SSL_CERT=/path/to/client-cert.pem  # SSL客户端证书路径
# MYSQL_SSL_KEY=/path/to/client-key.pem    # SSL客户端密钥路径

# =============================================================================
# 安全配置
# =============================================================================

# 查询安全限制
MAX_QUERY_LENGTH=10000             # 单个查询的最大字符长度
MAX_RESULT_ROWS=1000               # 查询结果的最大行数
QUERY_TIMEOUT=30                   # 查询执行超时时间（秒）

# 允许的SQL操作类型（逗号分隔）
ALLOWED_QUERY_TYPES=SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER

# 频率限制
RATE_LIMIT_WINDOW=60               # 频率限制时间窗口（秒）
RATE_LIMIT_MAX=100                 # 时间窗口内最大请求数

# =============================================================================
# 性能与缓存配置
# =============================================================================

# 缓存大小设置
SCHEMA_CACHE_SIZE=128              # 表结构缓存最大条目数
TABLE_EXISTS_CACHE_SIZE=64         # 表存在性检查缓存大小
INDEX_CACHE_SIZE=64                # 索引信息缓存大小
CACHE_TTL=300                      # 缓存过期时间（秒）

# 性能监控设置
SLOW_QUERY_THRESHOLD=1.0           # 慢查询识别阈值（秒）
METRICS_WINDOW_SIZE=1000           # 性能指标窗口大小
BATCH_SIZE=1000                    # 批处理大小

# 连接池健康监控
HEALTH_CHECK_INTERVAL=30           # 健康检查间隔（秒）
CONNECTION_MAX_AGE=3600            # 连接最大存活时间（秒）

# 重试配置
MAX_RETRY_ATTEMPTS=3               # 最大重试次数
MAX_RETRY_DELAY=10.0               # 最大重试延迟（秒）
RECONNECT_ATTEMPTS=3               # 重连尝试次数
RECONNECT_DELAY=1                  # 重连延迟（秒）

# =============================================================================
# 日志与调试配置
# =============================================================================

# 日志级别（DEBUG, INFO, WARNING, ERROR）
LOG_LEVEL=INFO
MAX_LOG_DETAIL_LENGTH=100          # 日志详情最大长度

# 日志配置
LOGGER_NAME=mysql_mcp              # 日志记录器名称

# =============================================================================
# 性能优化配置
# =============================================================================

# 百分位数计算阈值
PERCENTILE_SMALL_ARRAY_THRESHOLD=100    # 小数组阈值，低于此值使用排序算法

# 健康检查间隔
HEALTH_CHECK_INTERVAL=30.0              # 健康检查间隔（秒）

# 连接获取警告阈值
CONNECTION_ACQUISITION_WARNING_THRESHOLD=0.1  # 连接获取警告阈值（秒）

# 慢查询阈值
SLOW_QUERY_THRESHOLD=1.0                     # 慢查询阈值（秒）

# =============================================================================
# 不同环境的配置示例
# =============================================================================

# --- 开发环境配置 ---
# 适合本地开发，启用调试日志和较小的限制
# MYSQL_HOST=localhost
# MYSQL_CONNECTION_LIMIT=5
# MAX_RESULT_ROWS=500
# RATE_LIMIT_MAX=50
# CACHE_TTL=180
# LOG_LEVEL=DEBUG

# --- 生产环境配置 ---
# 适合生产部署，启用SSL和增强安全性
# MYSQL_HOST=prod-mysql-server
# MYSQL_CONNECTION_LIMIT=20
# MAX_RESULT_ROWS=2000
# RATE_LIMIT_MAX=200
# SCHEMA_CACHE_SIZE=256
# MYSQL_SSL=true
# LOG_LEVEL=WARNING

# --- 高性能环境配置 ---
# 适合高负载工作场景，大缓存和高并发
# MYSQL_CONNECTION_LIMIT=50
# MAX_RESULT_ROWS=5000
# RATE_LIMIT_MAX=500
# SCHEMA_CACHE_SIZE=512
# TABLE_EXISTS_CACHE_SIZE=256
# INDEX_CACHE_SIZE=256
# BATCH_SIZE=2000
# SLOW_QUERY_THRESHOLD=0.3

# --- 内存受限环境配置 ---
# 适合内存有限的环境，减少缓存使用
# MYSQL_CONNECTION_LIMIT=3
# MAX_RESULT_ROWS=200
# RATE_LIMIT_MAX=20
# SCHEMA_CACHE_SIZE=32
# TABLE_EXISTS_CACHE_SIZE=16
# INDEX_CACHE_SIZE=16
# BATCH_SIZE=100
# CACHE_TTL=120